# PLG运营-中间页自动唤起APP-功能测试用例

## 功能测试

### APP检测与唤起逻辑

#### TL-H5中间页APP已安装且不在灰度黑名单正常唤起验证

##### PD-前置条件：用户手机已安装e签宝APP；appId不在灰度黑名单中；使用安卓系统；网络连接正常；

##### 步骤一：在安卓手机浏览器中打开签署中间页链接

##### 步骤二：系统自动检测手机是否安装e签宝APP

##### 步骤三：系统检查当前appId是否在灰度黑名单中

##### 步骤四：系统判断检测结果并触发唤起逻辑

##### 步骤五：观察页面是否弹出引导打开APP的提示窗

##### 步骤六：点击引导提示中的"打开APP"按钮

##### ER-预期结果：1：系统成功检测到已安装APP；2：灰度判断通过，不在黑名单中；3：页面弹出引导打开APP的提示窗；4：点击后成功唤起e签宝APP；5：APP正常打开并跳转到对应功能页面；

#### TL-H5中间页APP未安装不触发唤起逻辑验证

##### PD-前置条件：用户手机未安装e签宝APP；appId不在灰度黑名单中；使用安卓系统；网络连接正常；

##### 步骤一：在安卓手机浏览器中打开签署中间页链接

##### 步骤二：系统自动检测手机是否安装e签宝APP

##### 步骤三：系统检查当前appId是否在灰度黑名单中

##### 步骤四：系统判断检测结果

##### 步骤五：观察页面是否有任何唤起相关的提示或弹窗

##### ER-预期结果：1：系统检测到未安装APP；2：灰度判断通过；3：页面不显示任何引导打开APP的提示；4：保持原有H5页面正常显示；5：用户可正常在H5页面完成操作；

#### TL-H5中间页appId在灰度黑名单中不触发唤起验证

##### PD-前置条件：用户手机已安装e签宝APP；当前appId在灰度黑名单中；使用安卓系统；网络连接正常；

##### 步骤一：运营后台将测试appId添加到功能灰度黑名单中

##### 步骤二：在安卓手机浏览器中打开签署中间页链接

##### 步骤三：系统自动检测手机是否安装e签宝APP

##### 步骤四：系统检查当前appId是否在灰度黑名单中

##### 步骤五：系统根据灰度配置决定是否执行唤起逻辑

##### 步骤六：观察页面是否有唤起相关提示

##### ER-预期结果：1：系统检测到已安装APP；2：灰度判断发现appId在黑名单中；3：系统不执行唤起逻辑；4：页面保持原样，不显示引导提示；5：用户可正常使用H5功能；

### 业务场景验证

#### TL-审批中间页APP唤起功能验证

##### PD-前置条件：用户手机已安装e签宝APP；appId不在灰度黑名单中；使用安卓系统；网络连接正常；

##### 步骤一：在安卓手机浏览器中打开审批中间页链接

##### 步骤二：系统自动检测手机是否安装e签宝APP

##### 步骤三：系统检查当前appId灰度配置

##### 步骤四：系统触发唤起逻辑

##### 步骤五：观察审批中间页的唤起提示展示

##### 步骤六：点击引导提示打开APP

##### ER-预期结果：1：审批中间页成功检测APP安装状态；2：灰度判断通过；3：显示审批场景的引导提示；4：成功唤起APP并跳转到审批功能；5：审批流程正常进行；

### 操作系统兼容性

#### TL-鸿蒙系统APP唤起功能兼容性验证

##### PD-前置条件：用户使用鸿蒙系统手机；手机已安装e签宝APP；appId不在灰度黑名单中；网络连接正常；

##### 步骤一：在鸿蒙系统手机浏览器中打开签署中间页

##### 步骤二：系统检测当前操作系统类型

##### 步骤三：系统执行APP安装状态检测

##### 步骤四：系统进行灰度配置判断

##### 步骤五：系统执行鸿蒙系统的APP唤起逻辑

##### 步骤六：观察唤起效果和APP打开情况

##### ER-预期结果：1：系统正确识别鸿蒙系统；2：APP检测功能在鸿蒙系统正常工作；3：唤起逻辑兼容鸿蒙系统；4：成功唤起e签宝APP；5：APP在鸿蒙系统中正常运行；

#### TL-iOS系统不触发APP唤起功能验证

##### PD-前置条件：用户使用iOS系统手机；手机已安装e签宝APP；appId不在灰度黑名单中；网络连接正常；

##### 步骤一：在iOS系统手机浏览器中打开签署中间页

##### 步骤二：系统检测当前操作系统类型

##### 步骤三：系统判断iOS系统不支持该功能

##### 步骤四：观察页面是否有任何唤起相关处理

##### 步骤五：验证H5页面正常功能

##### ER-预期结果：1：系统正确识别iOS系统；2：系统不执行APP检测逻辑；3：页面不显示任何唤起提示；4：H5页面功能正常可用；5：用户可在H5完成签署操作；

### 浏览器兼容性

#### TL-Chrome浏览器APP唤起功能验证

##### PD-前置条件：用户使用安卓系统Chrome浏览器；手机已安装e签宝APP；appId不在灰度黑名单中；网络连接正常；

##### 步骤一：打开Chrome浏览器访问签署中间页

##### 步骤二：系统检测浏览器类型和APP安装状态

##### 步骤三：系统执行Chrome浏览器的唤起逻辑

##### 步骤四：观察Chrome浏览器中的唤起提示展示

##### 步骤五：点击唤起提示验证APP打开效果

##### ER-预期结果：1：Chrome浏览器正常检测APP状态；2：唤起提示在Chrome中正确显示；3：Chrome浏览器成功唤起APP；4：APP正常打开并跳转；5：整个流程在Chrome中运行稳定；

#### TL-微信内置浏览器APP唤起功能验证

##### PD-前置条件：用户在微信内置浏览器中访问；使用安卓系统；手机已安装e签宝APP；appId不在灰度黑名单中；

##### 步骤一：通过微信打开签署中间页链接

##### 步骤二：系统检测微信内置浏览器环境

##### 步骤三：系统执行APP检测和灰度判断

##### 步骤四：系统触发微信环境下的唤起逻辑

##### 步骤五：观察微信内置浏览器的唤起效果

##### 步骤六：验证从微信跳转到APP的流程

##### ER-预期结果：1：系统识别微信内置浏览器环境；2：APP检测在微信环境正常工作；3：唤起逻辑适配微信环境；4：成功从微信跳转到e签宝APP；5：跳转后APP功能正常；

#### TL-UC浏览器APP唤起功能验证

##### PD-前置条件：用户使用安卓系统UC浏览器；手机已安装e签宝APP；appId不在灰度黑名单中；网络连接正常；

##### 步骤一：打开UC浏览器访问签署中间页

##### 步骤二：系统检测UC浏览器环境和APP状态

##### 步骤三：系统执行UC浏览器的唤起适配逻辑

##### 步骤四：观察UC浏览器中的唤起提示

##### 步骤五：点击提示验证APP唤起效果

##### ER-预期结果：1：UC浏览器环境正确识别；2：APP检测功能在UC浏览器正常；3：唤起逻辑兼容UC浏览器；4：成功唤起e签宝APP；5：UC浏览器到APP的跳转流畅；

#### TL-QQ浏览器APP唤起功能验证

##### PD-前置条件：用户使用安卓系统QQ浏览器；手机已安装e签宝APP；appId不在灰度黑名单中；网络连接正常；

##### 步骤一：打开QQ浏览器访问审批中间页

##### 步骤二：系统检测QQ浏览器环境

##### 步骤三：系统执行APP检测和灰度验证

##### 步骤四：系统触发QQ浏览器的唤起逻辑

##### 步骤五：验证QQ浏览器中的唤起提示展示

##### 步骤六：测试从QQ浏览器唤起APP的效果

##### ER-预期结果：1：QQ浏览器环境识别准确；2：APP检测在QQ浏览器中正常；3：唤起提示在QQ浏览器正确显示；4：成功从QQ浏览器唤起APP；5：APP打开后功能正常可用；

### 异常场景处理

#### TL-网络异常情况下APP检测功能验证

##### PD-前置条件：用户手机已安装e签宝APP；appId不在灰度黑名单中；使用安卓系统；网络连接不稳定；

##### 步骤一：在网络信号较弱环境下打开签署中间页

##### 步骤二：系统尝试进行APP检测

##### 步骤三：观察网络异常时的检测超时处理

##### 步骤四：验证检测失败时的降级处理

##### 步骤五：检查页面是否有异常提示或错误

##### ER-预期结果：1：系统检测到网络异常；2：APP检测在超时时间内完成或超时；3：检测失败时不显示唤起提示；4：页面保持正常H5功能可用；5：不影响用户正常使用H5完成操作；

### 灰度配置管理

#### TL-灰度配置实时生效验证

##### PD-前置条件：用户手机已安装e签宝APP；使用安卓系统；网络连接正常；运营后台可正常操作；

##### 步骤一：确认当前appId不在灰度黑名单中

##### 步骤二：在安卓手机打开签署中间页，验证唤起功能正常

##### 步骤三：运营后台将该appId添加到灰度黑名单

##### 步骤四：刷新签署中间页或重新打开

##### 步骤五：观察灰度配置变更后的功能表现

##### 步骤六：运营后台将该appId从黑名单中移除

##### 步骤七：再次验证唤起功能恢复

##### ER-预期结果：1：初始状态唤起功能正常；2：添加黑名单后唤起功能被禁用；3：配置变更实时生效；4：移除黑名单后唤起功能恢复；5：整个过程不影响H5基础功能；

### 性能验证

#### TL-多次访问中间页APP检测性能验证

##### PD-前置条件：用户手机已安装e签宝APP；appId不在灰度黑名单中；使用安卓系统；网络连接正常；

##### 步骤一：第一次打开签署中间页，记录APP检测响应时间

##### 步骤二：关闭页面后立即重新打开，记录第二次检测时间

##### 步骤三：连续多次打开关闭页面，记录每次检测时间

##### 步骤四：分析检测时间是否有缓存优化

##### 步骤五：验证检测结果的准确性和一致性

##### ER-预期结果：1：首次检测响应时间小于2秒；2：后续检测时间有缓存优化；3：多次检测结果保持一致；4：检测性能稳定不影响页面加载；5：唤起功能每次都能正常工作；
